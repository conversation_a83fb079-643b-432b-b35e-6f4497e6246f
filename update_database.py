#!/usr/bin/env python3
"""
数据库更新脚本 - 添加通信主题相关字段
"""

import sqlite3
import os
from pathlib import Path

def update_database():
    """更新数据库，添加新字段"""
    
    # 查找数据库文件
    db_paths = [
        'instance/app.db',
        'app.db',
        'instance/database.db'
    ]
    
    db_path = None
    for path in db_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ 未找到数据库文件，请确认数据库位置")
        return False
    
    print(f"📁 找到数据库文件: {db_path}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(devices)")
        columns = [column[1] for column in cursor.fetchall()]
        
        print(f"📋 当前设备表字段: {columns}")
        
        # 添加 communication_topics 字段
        if 'communication_topics' not in columns:
            print("➕ 添加 communication_topics 字段...")
            cursor.execute("ALTER TABLE devices ADD COLUMN communication_topics TEXT")
            print("✅ communication_topics 字段添加成功")
        else:
            print("ℹ️ communication_topics 字段已存在")
        
        # 添加 device_role 字段
        if 'device_role' not in columns:
            print("➕ 添加 device_role 字段...")
            cursor.execute("ALTER TABLE devices ADD COLUMN device_role VARCHAR(64)")
            print("✅ device_role 字段添加成功")
        else:
            print("ℹ️ device_role 字段已存在")
        
        # 创建索引
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_devices_device_role ON devices(device_role)")
            print("✅ 设备角色索引创建成功")
        except sqlite3.Error as e:
            print(f"⚠️ 索引创建警告: {e}")
        
        # 提交更改
        conn.commit()
        print("💾 数据库更新完成")
        
        # 验证更新
        cursor.execute("PRAGMA table_info(devices)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 更新后的设备表字段: {new_columns}")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ 数据库更新失败: {e}")
        return False
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🔧 开始更新数据库...")
    success = update_database()
    if success:
        print("🎉 数据库更新成功！现在可以重启应用程序。")
    else:
        print("💥 数据库更新失败，请检查错误信息。")
